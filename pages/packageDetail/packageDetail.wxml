<view class="container">
  <scroll-view scroll-y class="scroll-container">
    <!-- 包裹详情卡片 -->
    <view class="detail-card">
      <view class="orderCode">订单编号：{{ orderCode }}</view>
      <view class="logistics-status">
         <view class="location-item">日本</view>
        <view class="status-arrow">
          <image src="/assets/icons/logistics_rightArrow.png" mode="aspectFit"></image>
          <text>已签收</text>
        </view>
        <view class="location-item">{{recipient.provinceCityArea}}</view>
      </view>
      <view class="logistics-status">
        <view class="logistics-time">订单时间：{{ orderTime || '--'}}</view>
      </view>

      <block wx:for="{{packageList}}" wx:key="index">
        <view class="card-item">
          <!-- 物品属性选择 -->
          <view class="section">
            <view class="section-row">
              <text class="section-title">物品属性</text>
              <text class="section-content">{{item.goodsAttr === 1 ? 'A' : 'B'}}类</text>
            </view>
          </view>

          <!-- 英文品名 -->
          <view class="section">
            <view class="section-row">
              <text class="section-title">英文品名</text>
              <text class="section-content">{{item.enName || '根据货物自动生成关关'}}</text>
            </view>
          </view>

          <!-- 数量 -->
          <view class="section">
            <view class="section-row">
              <text class="section-title">数量</text>
              <text class="section-content">{{item.itemQuantity}} 件</text>
            </view>
          </view>

          <!-- 长*宽*高 -->
          <view class="section">
            <view class="section-row">
              <text class="section-title">长*宽*高</text>
              <!-- 使用三元运算符进行判断 -->
              <text class="section-content">
                {{ (item.chang && item.kuan && item.gao) ? item.chang + '*' + item.kuan + '*' + item.gao + ' cm' : '-' }}
              </text> 
            </view>
          </view>

          <!-- 重量 -->
          <view class="section">
            <view class="section-row">
              <text class="section-title">重量</text>
              <text class="section-content">{{item.itemWeight}} kg</text>
            </view>
          </view>
        </view>
        <view class="divider" wx:if="{{index !== packageList.length - 1}}"></view>
      </block>
      <!-- 物流信息列表 -->
      <view class="logistics-list" wx:if="{{logistics.length > 0}}">
        <view class="section-title">物流信息</view>
        <view
          class="logistics-item"
          wx:for="{{logistics}}"
          wx:key="id"
          bindtap="handleItemClick"
          data-id="{{item.id}}"
          data-tracking-number="{{item.trackingNumber}}"
        >
          <!-- 第一行：快递公司 -->
          <view class="item-row company">快递公司：{{ expressCompanyMap[item.expressCompanyId] || '未知公司' }}</view>
          <!-- 第二行：物流单号 -->
          <view class="item-row tracking">物流单号：{{ item.trackingNumber }}</view>
          <!-- 右侧箭头 -->
          <image class="arrow-icon" src="/assets/icons/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>

      <!-- 无物流信息时显示 -->
      <view class="no-logistics" wx:else>
        <text>暂无物流信息</text>
      </view>
    </view>
  </scroll-view>
</view>
