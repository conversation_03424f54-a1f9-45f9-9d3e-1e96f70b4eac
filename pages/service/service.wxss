.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.message-list {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
}

.message-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30rpx;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.message-item.me .avatar {
  margin-right: 0;
  margin-left: 20rpx;
  order: 1;
}

.message-content {
  max-width: 70%;
  padding: 15rpx 20rpx;
  border-radius: 10rpx;
  background: #f0f0f0;
}

.message-item.me {
  justify-content: flex-end;
}

.message-item.me .message-content {
  background: #FA7C00;
  color: white;
  margin-left: 0;
  margin-right: 20rpx;
}

.message-time {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.input-box {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: white;
  border-top: 1rpx solid #eee;
}

.input {
  flex: 1;
  margin-right: 20rpx;
  padding: 15rpx;
  border: 1rpx solid #eee;
  border-radius: 30rpx;
}

.send-btn {
  background: #FA7C00;
  color: white;
  border-radius: 30rpx;
  padding: 0 20rpx;
  max-width: 20%;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 26rpx;
}

/* rich-text 组件样式 */
.message-content rich-text {
  line-height: 1.6;
  word-break: break-all;
  font-size: 28rpx;
  color: #333;
}

/* 当消息是用户发送时，rich-text文本颜色为白色 */
.message-item.me .message-content rich-text {
  color: white;
}