<view class="container">
  <view class="message-list">
    <block wx:for="{{messages}}" wx:key="id">
      <view class="message-item {{item.isMe ? 'me' : ''}}">
        <image class="avatar" src="{{item.isMe ? '/assets/icons/default-avatar.png' : '/assets/icons/service.png'}}"></image>
        <view class="message-content">
          <rich-text wx:if="{{!item.isMe && item.hasHtml}}" nodes="{{item.content}}"></rich-text>
          <text wx:else>{{item.content}}</text>
        </view>
      </view>
    </block>
  </view>

  <view class="input-box">
    <input 
      class="input" 
      placeholder="请输入消息" 
      value="{{messageInput}}"
      bindinput="onInputChange"
    />
    <button class="send-btn" bindtap="sendMessage">发送</button>
  </view>
</view>