import { get } from '../../utils/request'

Page({
  data: {
    messages: [],
    messageInput: '' // 新增输入框内容的变量
  },

  // 输入框内容变化时更新数据
  onInputChange(e) {
    this.setData({
      messageInput: e.detail.value
    });
  },

  onLoad() {
    this.setData({
      messages: [
        {
          id: 1,
          content: '请问有什么可以帮您的？',
          isMe: false,
          hasHtml: false
        }
      ]
    });
  },

  // 检测内容是否包含HTML标签
  hasHtmlTags(content) {
    return /<[^>]*>/g.test(content);
  },

  // 发送消息
  async sendMessage() {
    const message = this.data.messageInput.trim();
    if (!message) {
      wx.showToast({
        title: '请输入消息内容',
        icon: 'none'
      });
      return;
    }

    console.log('发送的消息：', message);
    const res = await get('/app/wchat/kefu', { request: message });
    if (res.code === '200') {
      console.log('收到的消息：', res.content);

      // 检测返回内容是否包含HTML
      const hasHtml = this.hasHtmlTags(res.content);

      this.setData({
        messages: [
          ...this.data.messages,
          {
            id: this.data.messages.length + 1,
            content: message,
            isMe: true,
            hasHtml: false
          },
          {
            id: this.data.messages.length + 2,
            content: res.content,
            isMe: false,
            hasHtml: hasHtml
          }
        ],
        messageInput: '' // 清空输入框
      });
    }
  }
})