Page({
  data: {
    goodsAttr: 'A',
    currencyList: [
      { name: 'CNY', value: 'CNY' },
      { name: 'USD', value: 'USD' },
      { name: 'EUR', value: 'EUR' }
    ],
    currencyIndex: 0,
    addedCount: 0,
    enName: '',
    customsCode: '',
    material: '',
    purpose: '',
    itemPrice: '',
    itemQuantity: '',
    itemWeight: '', // 添加申报重量字段
    chang: '',
    kuan: '',
    gao: '',
    newGoods: [],
  },

  // 切换物品属性
  changePropertyType(e) {
    this.setData({
      goodsAttr: e.currentTarget.dataset.type
    });
  },

  // 复制链接
  copyLink() {
    wx.setClipboardData({
      data: 'https://www.hsbianma.com',
      success: () => {
        wx.showToast({
          title: '复制成功',
          icon: 'none'
        });
      }
    });
  },

  // 货币选择
  bindCurrencyChange(e) {
    this.setData({
      currencyIndex: e.detail.value
    });
  },

  // 表单验证方法
  validateForm() {
    const fields = [
      { field: 'enName', label: '英文品名' },
      { field: 'customsCode', label: '海关编码' },
      { field: 'material', label: '材质' },
      { field: 'purpose', label: '用途' },
      { field: 'itemPrice', label: '物品单价' },
      { field: 'itemQuantity', label: '物品数量' },
      { field: 'itemWeight', label: '申报重量' },
      { field: 'chang', label: '长度' },
      { field: 'kuan', label: '宽度' },
      { field: 'gao', label: '高度' }
    ];

    for (const { field, label } of fields) {
      if (!this.data[field]) {
        wx.showToast({
          title: `请输入${label}`,
          icon: 'none'
        });
        return false;
      }
    }
    return true;
  },
  // 继续添加
  addMore() {
    if (!this.validateForm()) return;
  
    // 创建物品数据对象
    const goodsItem = this.createGoodsItem();

    this.setData({
      newGoods: [...this.data.newGoods, goodsItem]
    })
  
    // 清空当前表单
    this.setData({
      goodsAttr: 'A',
      enName: '',
      customsCode: '',
      material: '',
      purpose: '',
      itemPrice: '',
      itemQuantity: '',
      currencyIndex: 0,
      chang: '',
      kuan: '',
      gao: '',
      itemWeight: '', // 清空申报重量
      addedCount: this.data.addedCount + 1
    });
  
    // wx.showToast({
    //   title: '添加成功',
    //   icon: 'success'
    // });
  },

  // 确认提交
  confirmSubmit() {
    if (!this.validateForm()) return;
  
    // 创建物品数据对象
    const currentItem = this.createGoodsItem();
    // 获取页面实例
    const pages = getCurrentPages();
    const prevPage = pages[pages.length - 2];
  
    // 更新上一个页面的数据
    const tempArray = prevPage.data.goods || [];
    if (this.data.newGoods.length > 0) {
      tempArray.push(...this.data.newGoods);
    } 
    tempArray.push(currentItem);
    prevPage.setData({
      goods: tempArray
    });
  
    // 返回上一页
    wx.navigateBack({
      success: () => {
        wx.showToast({
          title: '添加成功',
          icon: 'success'
        });
      }
    });
  },

  // 抽取创建 goodsItem 的方法
  createGoodsItem() {
    const goodsItem = {
      goodsAttr: this.data.goodsAttr,
      enName: this.data.enName,
      customsCode: this.data.customsCode,
      material: this.data.material,
      purpose: this.data.purpose,
      itemPrice: this.data.itemPrice,
      currency: this.data.currencyList[this.data.currencyIndex].value,
      itemQuantity: this.data.itemQuantity,
      itemWeight: this.data.itemWeight,
      chang: this.data.chang,
      kuan: this.data.kuan,
      gao: this.data.gao
    };
    return goodsItem;
  },

  // 处理数字和小数点输入的公共方法
  handleDecimalInput(e, field) {
    const value = e.detail.value;
    // 只允许输入数字和小数点
    if (value === '') return value;
    
    // 检查是否只包含数字和小数点
    if (!/^[\d.]*$/.test(value)) {
      return this.data[field] || '';
    }
    
    // 检查小数点数量
    const dotCount = value.split('.').length - 1;
    if (dotCount > 1) {
      return this.data[field] || '';
    }
    
    this.setData({
      [field]: value
    });
    return value;
  },

  // 处理物品单位输入
  handleUnitInput(e) {
    return this.handleDecimalInput(e, 'itemPrice');
  },

  // 处理申报重量输入
  handleWeightInput(e) {
    return this.handleDecimalInput(e, 'itemWeight');
  },

  // 处理物品数量输入
  handleQuantityInput(e) {
    const value = e.detail.value;
    // 只允许输入数字
    if (value === '') return value;
    
    if (!/^\d*$/.test(value)) {
      return this.data.itemQuantity || '';
    }
    
    this.setData({
      itemQuantity: value
    });
    return value;
  },
  
  // 添加输入框的处理函数
  onInput(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    this.setData({
      [field]: value
    });
  },

  // 处理物品单位输入框失焦
  handleUnitBlur(e) {
    this.handleNumberBlur(e, 'itemPrice');
  },

  // 处理申报重量失去焦点
  handleWeightBlur(e) {
    this.handleNumberBlur(e, 'itemWeight');
  },
  // 通用的数字输入框失焦处理
  handleNumberBlur(e, field) {
    let value = e.detail.value;
    if (value) {
      // 处理以小数点结尾的情况
      if (value.endsWith('.')) {
        value = value.slice(0, -1);
      }
      // 确保是有效数字
      if (!isNaN(parseFloat(value))) {
        // 保留两位小数并更新到输入框
        const formattedValue = parseFloat(value).toFixed(2);
        this.setData({
          [field]: formattedValue
        });
        // 如果是单位输入框，需要手动触发更新
        if (field === 'itemPrice') {
          const input = this.selectComponent('.unit-input');
          if (input) {
            input.value = formattedValue;
          }
        }
      }
    }
  }

});
