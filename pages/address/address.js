import { get } from '../../utils/request'

Page({
  data: {
    addresses: [],
    searchText: '',
    filteredAddresses: []
  },
  onShow() {
    this.loadAddresses()
  },

  // 加载地址列表
  async loadAddresses() {
    try {
      const res = await get('/app/wchat/recipientList')
      if (res.code === '200') {
        const addresses = res.content.map(item => ({
          id: item.id,
          chineseName: item.chineseName,
          englishName: item.englishName,
          phone: item.phone,
          provinceCityArea: item.provinceCityArea,
          addressDetails: item.addressDetails,
          companyName: item.companyName,
          email: item.email,
          idFront: item.idFront,
          idBack: item.idBack,
          isDefault: item.isDefault === 1,
          memberId: item.memberId,
          postalCode: item.postalCode
        }))

        this.setData({
          addresses,
          filteredAddresses: addresses
        })
      }
    } catch (error) {
      wx.showToast({
        title: '获取地址列表失败',
        icon: 'none'
      })
    }
  },

  // 搜索地址
  onSearchInput(e) {
    const searchText = e.detail.value.toLowerCase()
    this.setData({ searchText })
    
    const filteredAddresses = this.data.addresses.filter(address => {
      return address.chineseName.toLowerCase().includes(searchText) ||
             address.phone.includes(searchText) ||
             address.provinceCityArea.toLowerCase().includes(searchText) ||
             address.addressDetails.toLowerCase().includes(searchText)
    })
    
    this.setData({ filteredAddresses })
  },

  // 点击地址项选择地址
  onSelectAddress(e) {
    const { id } = e.currentTarget.dataset
    const address = this.data.addresses.find(a => a.id === id)
    // 获取页面栈
    const pages = getCurrentPages()
    const sendPackagePage = pages[pages.length - 2]

    // 如果是从寄件页面进来的
    if (sendPackagePage && sendPackagePage.route === 'pages/sendPackage/sendPackage') {
      // 更新寄件页面的地址信息
      sendPackagePage.setData({
        selectedReceiveAddress: address
      })
      wx.navigateBack()
    }
  },

  // 点击编辑地址
  onEditAddress(e) {
    const { id } = e.currentTarget.dataset;

    // 直接跳转到编辑页面，传递ID参数，让编辑页面从网络获取数据
    wx.navigateTo({
      url: `/pages/receiver/receiver?type=edit&id=${id}`
    });
  },

  // 点击新增地址
  onAddAddress() {
    wx.navigateTo({
      url: '/pages/receiver/receiver?type=add'
    })
  },

  // 删除地址
  async onDeleteAddress(e) {
    const { id } = e.currentTarget.dataset
    
    wx.showModal({
      title: '提示',
      content: '确定要删除这个地址吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            const res = await get('/app/wchat/recipientDrop', { id })
            if (res.code === '200') {
              // 删除成功后更新本地数据
              const addresses = this.data.addresses.filter(address => address.id !== id)
              this.setData({
                addresses,
                filteredAddresses: this.filterAddresses(addresses, this.data.searchText)
              })
              
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              })
            } else {
              wx.showToast({
                title: res.message || '删除失败',
                icon: 'none'
              })
            }
          } catch (error) {
            console.error('删除地址失败：', error)
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            })
          }
        }
      }
    })
  },
  // 过滤地址列表
  filterAddresses(addresses, searchText) {
    if (!searchText) return addresses
    
    return addresses.filter(address => {
      return address.name.toLowerCase().includes(searchText.toLowerCase()) ||
             address.phone.includes(searchText) ||
             address.region.toLowerCase().includes(searchText.toLowerCase()) ||
             address.detail.toLowerCase().includes(searchText.toLowerCase())
    })
  }
})