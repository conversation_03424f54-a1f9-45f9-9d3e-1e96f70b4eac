import { post } from '../../utils/request'

Page({
  data: {
    goods: [],
    totalValue: 0,
    totalWeight: 0,
    recentRecords: [
      {
        date: '2024-01-15',
        desc: '进口零食、护肤品、服装',
        status: '已通关'
      },
      {
        date: '2024-01-10',
        desc: '电子产品、保健品',
        status: '已通关'
      }
    ]
  },

  // 删除物品
  deleteGoods(e) {
    const index = e.currentTarget.dataset.index
    const goods = this.data.goods
    goods.splice(index, 1)
    this.setData({ goods })
    this.calculateTotal()
  },

  // 显示添加物品表单（进入申报信息页面）
  showAddGoodsForm() {
    wx.navigateTo({
      url: '/pages/report/report'
    })
  },

  // 计算总价值
  async calculateTotal() {
    console.log('计算总价值：', this.data.goods)

    // 如果没有物品数据，重置为0
    if (!this.data.goods || this.data.goods.length === 0) {
      this.setData({
        totalValue: 0,
        totalWeight: 0
      });
      return;
    }

    // 构造请求参数
    const requestData = {
      appltDatas: this.data.goods,
      totalPrice: 0,
      totalWeight: 0
    };

    console.log('调用计费接口，请求参数：', requestData);

    try {
      // 调用计费接口
      const res = await post('/app/wchat/calFee', requestData);

      if (res.code === '200') {
        console.log('计费接口返回数据：', res);

        // 获取最外层的 totalPrice 和 totalWeight
        const { totalPrice, totalWeight } = res.content || {};

        // 更新页面数据
        this.setData({
          totalValue: totalPrice || 0,
          totalWeight: totalWeight || 0
        });

        console.log('更新后的总价值和总重量：', { totalPrice, totalWeight });
      } else {
        console.error('计费接口返回错误：', res);
        wx.showToast({
          title: res.msg || '计算费用失败',
          icon: 'none'
        });
        // 接口失败时保持当前数据不变
      }
    } catch (error) {
      console.error('调用计费接口失败：', error);
      wx.showToast({
        title: '网络错误，计算费用失败',
        icon: 'none'
      });
      // 网络错误时保持当前数据不变
    }
  },

  // 提交申报信息
  submitDeclaration() {
    if (this.data.goods.length === 0) {
      wx.showToast({
        title: '请添加申报物品',
        icon: 'none'
      })
      return
    }

    // 获取当前页面实例
    const pages = getCurrentPages()
    const prevPage = pages[pages.length - 2]
    // 更新上一页的数据
    prevPage.setData({
      declarationInfo: {
        totalValue: this.data.totalValue,
        totalWeight: this.data.totalWeight,
        goods: this.data.goods
      }
    })

    wx.navigateBack()
  },

  onShow() {
    // 页面显示时重新计算总价值
    this.calculateTotal()
  }
})