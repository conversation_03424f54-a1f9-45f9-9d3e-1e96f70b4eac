import { post } from '../../utils/request'

Page({
  data: {
    goods: [],
    totalValue: 0,
    totalWeight: 0,
    recentRecords: [
      {
        date: '2024-01-15',
        desc: '进口零食、护肤品、服装',
        status: '已通关'
      },
      {
        date: '2024-01-10',
        desc: '电子产品、保健品',
        status: '已通关'
      }
    ]
  },

  // 删除物品
  deleteGoods(e) {
    const index = e.currentTarget.dataset.index
    const goods = this.data.goods
    goods.splice(index, 1)
    this.setData({ goods })
    this.calculateTotal()
  },

  // 显示添加物品表单（进入申报信息页面）
  showAddGoodsForm() {
    wx.navigateTo({
      url: '/pages/report/report'
    })
  },

  // 计算总价值
  async calculateTotal() {
    console.log('计算总价值：', this.data.goods)

    // 如果没有物品数据，重置为0
    if (!this.data.goods || this.data.goods.length === 0) {
      this.setData({
        totalValue: 0,
        totalWeight: 0
      });
      return;
    }

    // 给每个物品添加 totalPrice 和 totalWeight 字段，并进行数据类型转换
    const goodsWithTotals = this.data.goods.map(item => ({
      chang: parseInt(item.chang) || 0,                    // integer(int32)
      customsCode: String(item.customsCode || ''),         // string
      enName: String(item.enName || ''),                   // string
      gao: parseInt(item.gao) || 0,                        // integer(int32)
      goodsAttr: item.goodsAttr === 'A' ? 1 : (item.goodsAttr === 'B' ? 2 : (parseInt(item.goodsAttr) || 0)), // integer(int32) - A类:1 B类:2
      itemPrice: parseInt(item.itemPrice/100) || 0,            // integer(int32)
      itemQuantity: parseInt(item.itemQuantity) || 0,      // integer(int32)
      itemWeight: parseFloat(item.itemWeight) || 0,        // number
      kuan: parseInt(item.kuan) || 0,                      // integer(int32)
      material: String(item.material || ''),               // string
      purpose: String(item.purpose || ''),                 // string
      totalPrice: 0,                                       // integer(int64)
      totalWeight: 0                                       // number
    }));

    // 构造请求参数
    const requestData = {
      appltDatas: goodsWithTotals,
      totalPrice: 0,                                       // integer(int64)
      totalWeight: 0                                       // number
    };

    console.log('调用计费接口，请求参数：', requestData);

    try {
      // 调用计费接口
      const res = await post('/app/wchat/calFee', requestData);

      if (res.code === '200') {
        console.log('计费接口返回数据：', res);

        // 获取最外层的 totalPrice 和 totalWeight
        const { totalPrice, totalWeight } = res.content || {};

        // 更新页面数据
        this.setData({
          totalValue: totalPrice || 0,
          totalWeight: totalWeight || 0
        });

        console.log('更新后的总价值和总重量：', { totalPrice, totalWeight });
      } else {
        console.error('计费接口返回错误：', res);
        wx.showToast({
          title: res.msg || '计算费用失败',
          icon: 'none'
        });
        // 接口失败时保持当前数据不变
      }
    } catch (error) {
      console.error('调用计费接口失败：', error);
      wx.showToast({
        title: '网络错误，计算费用失败',
        icon: 'none'
      });
      // 网络错误时保持当前数据不变
    }
  },

  // 提交申报信息
  submitDeclaration() {
    if (this.data.goods.length === 0) {
      wx.showToast({
        title: '请添加申报物品',
        icon: 'none'
      })
      return
    }

    // 获取当前页面实例
    const pages = getCurrentPages()
    const prevPage = pages[pages.length - 2]
    // 更新上一页的数据
    prevPage.setData({
      declarationInfo: {
        totalValue: this.data.totalValue,
        totalWeight: this.data.totalWeight,
        goods: this.data.goods
      }
    })

    wx.navigateBack()
  },

  onShow() {
    // 页面显示时重新计算总价值
    this.calculateTotal()
  }
})