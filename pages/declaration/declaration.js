Page({
  data: {
    goods: [],
    totalValue: 0,
    totalWeight: 0,
    recentRecords: [
      {
        date: '2024-01-15',
        desc: '进口零食、护肤品、服装',
        status: '已通关'
      },
      {
        date: '2024-01-10',
        desc: '电子产品、保健品',
        status: '已通关'
      }
    ]
  },

  // 删除物品
  deleteGoods(e) {
    const index = e.currentTarget.dataset.index
    const goods = this.data.goods
    goods.splice(index, 1)
    this.setData({ goods })
    this.calculateTotal()
  },

  // 显示添加物品表单（进入申报信息页面）
  showAddGoodsForm() {
    wx.navigateTo({
      url: '/pages/report/report'
    })
  },

  // 计算总价值
  calculateTotal() {
    console.log('计算总价值：', this.data.goods)


    let calFee = {'appltDatas': this.data.goods, 'totalPrice': 0, 'totalWeight': 0};


    // let calculatedVolumePrice = 0
    // let total = 0
    // let weight = 0
    // const WEIGHT_LIMIT = 3  // 限重3公斤
    // const PRICE_A = 65      // A类价格：65元/公斤
    // const PRICE_B = 95      // B类价格：95元/公斤

   
    // this.data.goods.forEach(item => {
    //   // 计算总重量：单件重量 
    //   const itemTotalWeight = parseFloat(item.itemWeight || 0) 
    //   weight += itemTotalWeight

    //   // 根据物品属性计算价格
    //   const unitPrice = item.goodsAttr === 'A' ? PRICE_A : PRICE_B
      
    //   // 如果超过限重，给出提示
    //   if (itemTotalWeight > WEIGHT_LIMIT) {
    //     wx.showToast({
    //       title: `${item.goodsAttr}类物品超出限重${WEIGHT_LIMIT}kg`,
    //       icon: 'none',
    //       duration: 2000
    //     })
    //   }

    //   // 计算该物品的总价值：单价(根据重量) * 重量
    //   total += unitPrice * itemTotalWeight
    //   // 按体积计算价格 
    //   calculatedVolumePrice += (parseFloat(item.chang) * parseFloat(item.kuan) * parseFloat(item.gao)) / 6000 * unitPrice;               
    // })

    // // 取较大值作为该物品的价格
    // const itemPrice = Math.max(calculatedVolumePrice, total);
    
    // this.setData({
    //   totalValue: itemPrice.toFixed(2),
    //   totalWeight: weight.toFixed(2)
    // })
  },

  // 提交申报信息
  submitDeclaration() {
    if (this.data.goods.length === 0) {
      wx.showToast({
        title: '请添加申报物品',
        icon: 'none'
      })
      return
    }

    // 获取当前页面实例
    const pages = getCurrentPages()
    const prevPage = pages[pages.length - 2]
    // 更新上一页的数据
    prevPage.setData({
      declarationInfo: {
        totalValue: this.data.totalValue,
        totalWeight: this.data.totalWeight,
        goods: this.data.goods
      }
    })

    wx.navigateBack()
  },

  onShow() {
    // 页面显示时重新计算总价值
    this.calculateTotal()
  }
})