import { get, post, uploadFile } from '../../utils/request'

Page({
  data: {
    id: null,
    type: 'add', // add 或 edit
    form: {
      chineseName: '', // 中文姓名
      englishName: '', // 英文姓名
      phone: '', // 手机号
      postalCode: '', // 邮编
      provinceCityArea: '', // 省市区
      addressDetails: '', // 详细地址
      companyName: '', // (选填)公司名称
      email: '', // (选填)电子邮箱
      isDefault: 0, // 是否为默认地址
    },
    idFront: '', // 正面照片
    idBack: '', // 反面照片
    addressText: '', // 地址识别文本
    memberId: null, // 会员ID
  },
  onLoad(options) {
    const { type, id } = options;
    console.log('options 接收到的参数:', options);

    wx.setNavigationBarTitle({
      title: type === 'edit' ? '编辑收件人信息' : '添加收件人信息'
    });

    this.setData({ type, id });

    if (type === 'edit' && id) {
      // 从网络获取收件人信息
      this.loadRecipientInfo(id);
    }
  },

  // 从网络获取收件人信息
  async loadRecipientInfo(id) {
    try {
      wx.showLoading({
        title: '加载中...',
        mask: true
      });

      console.log('正在获取收件人信息，ID:', id);

      // 获取收件人列表，然后找到对应ID的收件人
      const res = await get('/app/wchat/recipientList');

      if (res.code === '200') {
        console.log('收件人列表获取成功:', res.content);
        const recipient = res.content.find(item => item.id == id);

        if (recipient) {
          this.setData({
            form: {
              chineseName: recipient.chineseName || '',
              englishName: recipient.englishName || '',
              phone: recipient.phone || '',
              postalCode: recipient.postalCode || '',
              provinceCityArea: recipient.provinceCityArea || '',
              addressDetails: recipient.addressDetails || '',
              companyName: recipient.companyName || '',
              email: recipient.email || '',
              isDefault: recipient.isDefault || 0
            },
            idFront: recipient.idFront || '',
            idBack: recipient.idBack || ''
          });
          console.log('从网络加载的收件人数据:', recipient);
        } else {
          console.error('未找到ID为', id, '的收件人信息');
          wx.showToast({
            title: '收件人信息不存在',
            icon: 'none'
          });
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        }
      } else {
        console.error('获取收件人列表失败:', res);
        wx.showToast({
          title: res.msg || '获取收件人信息失败',
          icon: 'none'
        });
        // 网络获取失败时，不自动返回，让用户可以重试
      }
    } catch (error) {
      console.error('获取收件人信息失败：', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
      // 网络错误时，不自动返回，让用户可以重试
    } finally {
      wx.hideLoading();
    }
  },
  // 输入框内容变化处理
  onInput(e) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail
    this.setData({
      [`form.${field}`]: value
    })
  },

  // 选择图片
  async chooseImage(e) {
    const { type } = e.currentTarget.dataset
    wx.showActionSheet({
      itemList: ['拍照', '从相册选择'],
      success: async (res) => {
        try {
          const sourceType = res.tapIndex === 0 ? ['camera'] : ['album']
          const imageRes = await wx.chooseImage({
            count: 1,
            sizeType: ['compressed'],
            sourceType: sourceType
          })
          
          // 显示上传中的loading
          wx.showLoading({
            title: '上传中...',
            mask: true
          })
          
          try {
            // 调用上传方法
            const uploadRes = await uploadFile(imageRes.tempFilePaths[0])
            if (uploadRes.code === '200') {
              console.log('上传成功====', uploadRes)
              // 上传成功，设置返回的图片URL
              this.setData({
                [type]: uploadRes.content
              })
            } else {
              wx.showToast({
                title: '上传失败',
                icon: 'none'
              })
            }
          } catch (uploadError) {
            console.error('上传失败', uploadError)
            wx.showToast({
              title: '上传失败',
              icon: 'none'
            })
          } finally {
            wx.hideLoading()
          }
        } catch (error) {
          console.log('选择图片失败', error)
          wx.showToast({
            title: '选择图片失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        console.log('选择失败', err)
      }
    })
  },

  // 选择地址
  chooseLocation() {
    wx.chooseLocation({
        success: (res) => {
            const location = {
                name: res.name,
                address: res.address,
                latitude: res.latitude,
                longitude: res.longitude
            };
            console.log('Selected Location:', location); // 调试信息

            // 更新表单数据，将地址和名称拼接
            this.setData({
                'form.addressDetails': `${location.address}${location.name}`,
            });
        },
        fail: (err) => {
            console.error('选择位置失败:', err);
            wx.showToast({
                title: '选择位置失败',
                icon: 'none'
            });
        }
    });
 },  
  // 提交表单
  async submitForm() {
    const { form, idFront, idBack, type, id } = this.data
    // 表单验证
    const requiredFields = {
      chineseName: '中文姓名',
      englishName: '英文姓名',
      phone: '手机号',
      provinceCityArea: '省市区',
      addressDetails: '详细地址'
    }
    
    // 检查必填字段
    for (const [field, label] of Object.entries(requiredFields)) {
      if (!form[field]) {
        wx.showToast({
          title: `请填写${label}`,
          icon: 'none'
        })
        return
      }
    }
    
    // 验证手机号格式
    const phoneReg = /^1[3-9]\d{9}$/
    if (!phoneReg.test(form.phone)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      })
      return
    }
    
    // 验证邮编格式（如果填写了的话）
    if (!form.postalCode) {
      wx.showToast({
        title: '请输入邮编',
        icon: 'none'
      })
      return
    }
    
    // 验证邮箱格式（如果填写了的话）
    if (form.email && !/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(form.email)) {
      wx.showToast({
        title: '请输入正确的邮箱',
        icon: 'none'
      })
      return
    }
    
    // 验证身份证照片
    if (!idFront || !idBack) {
      wx.showToast({
        title: '请上传身份证正反面照片',
        icon: 'none'
      })
      return
    }

    // 准备提交的数据
    const submitData = {
      chineseName: form.chineseName,
      englishName: form.englishName,
      phone: form.phone,
      postalCode: form.postalCode,
      provinceCityArea: form.provinceCityArea,
      addressDetails: form.addressDetails,
      companyName: form.companyName,
      email: form.email,
      idFront: idFront,
      idBack: idBack,
      id: id // 编辑模式下的ID
    }
console.log('提交收件人信息：', submitData)
    try {
      let res;
      if (type === 'add') {
        res = await post('/app/wchat/recipientAdd', submitData)
      } else {
        console.log('更新提交收件人信：',submitData)
        res = await post('/app/wchat/recipientUpdate', submitData)
      }
      
      if (res.code === '200') {
        wx.showToast({
          title: type === 'edit' ? '修改成功' : '添加成功',
          icon: 'success'
        })
        
        // 返回上一页并刷新
        setTimeout(() => {
          const pages = getCurrentPages()
          const prevPage = pages[pages.length - 2]
          if (prevPage) {
            prevPage.onLoad() // 刷新上一页数据
          }
          wx.navigateBack()
        }, 1500)
      }
    } catch (error) {
      console.error('提交收件人信息失败：', error)
    }
  },

  // 清空表单
  resetForm() {
    this.setData({
      form: {
        chineseName: '',
        englishName: '',
        phone: '',
        postalCode: '',
        provinceCityArea: '',  // 确保使用 provinceCityArea
        addressDetails: '',
        companyName: '',
        email: ''
      },
      idFront: '',
      idBack: ''
    })
  },
  // 省市区选择器变化处理
  onRegionChange(e) {
    const province = e.detail.value.join(' ')
    this.setData({
      'form.provinceCityArea': province  // 修改这里，将数据保存到 provinceCityArea 字段
    })
  },
  // 地址文本输入
  onAddressInput(e) {
    this.setData({
      addressText: e.detail.value
    });
  },

  // 识别地址
  recognizeAddress() {
    const text = this.data.addressText;
    if (!text) {
      wx.showToast({
        title: '请输入地址信息',
        icon: 'none'
      });
      return;
    }

    // 识别手机号
    const phoneReg = /1[3-9]\d{9}/;
    const phone = text.match(phoneReg)?.[0] || '';
    
    // 识别邮编（6位数字）和常见邮箱后缀
    const postcodeReg = /\b\d{6}\b|@qq\.com|@163\.com|@126\.com|@gmail\.com|@hotmail\.com|@outlook\.com|@yahoo\.com/;
    const postalCode = text.match(postcodeReg)?.[0] || '';

    // 识别电子邮件
    const emailReg = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/;
    const email = text.match(emailReg)?.[0] || '';
    
    // 移除手机号、邮编和邮箱后的文本
    let remainText = text
      .replace(phoneReg, '')
      .replace(postcodeReg, '')
      .replace(emailReg, '')
      .trim();
    
    // 假设姓名在最前面，取前三个字符作为姓名
    const name = remainText.slice(0, 3).trim();
    
    // 剩余部分作为地址
    const address = remainText.slice(3).trim();

    // 更新表单数据
    this.setData({
      'form.chineseName': name,
      'form.phone': phone,
      'form.postalCode': postalCode,
      'form.email': email,
      'form.addressDetails': address,
      addressText: '' // 清空输入框
    });

    wx.showToast({
      title: '识别成功',
      icon: 'success'
    });
  },
});