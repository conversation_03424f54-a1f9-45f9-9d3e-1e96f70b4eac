const BASE_URL = 'https://miniapp.hh2mwa.top/ms' // HTTPS配置正常

const request = (options) => {
  return new Promise((resolve, reject) => {
    // 获取存储的token 
    const userSession = wx.getStorageSync('userSession')
    const token = userSession ? userSession.token : ''
    wx.showLoading({ title: '加载中...', mask: true })

    // 构建请求头
    const headers = {
      'Content-Type': 'application/json',
      'token': token,
      ...options.header
    }
    
    // 打印请求信息
    console.log('请求URL:', `${BASE_URL}${options.url}`)
    console.log('请求方法:', options.method || 'GET')
    console.log('请求头:', headers)
    console.log('请求数据:', options.data)

    wx.request({
      url: `${BASE_URL}${options.url}`,
      method: options.method || 'GET',
      data: options.data,
      header: headers,
      success: (res) => {
        console.log('请求响应:', res);
        // 处理成功响应
        if (res.data && res.data.code === '200') {
          resolve(res.data)
        } else if (res.data && res.data.code === '401') { // token 失效
          // 清除登录信息
          wx.clearStorageSync()
          // 跳转到登录页
          wx.redirectTo({
            url: '/pages/login/login'
          })
          reject(res.data)
        } else {
          console.error('请求失败，服务器返回:', res.data);
          wx.showToast({
            title: (res.data && res.data.msg) || '请求失败',
            icon: 'none'
          })
          reject(res.data || { code: 'UNKNOWN', msg: '未知错误' })
        }
      },
      fail: (err) => {
        console.error('请求失败详情:', err)
        console.error('请求URL:', `${BASE_URL}${options.url}`)
        console.error('错误信息:', err.errMsg)

        // 更详细的错误分析
        let errorMessage = '网络错误';
        if (err.errMsg) {
          if (err.errMsg.includes('request:fail url not in domain list')) {
            errorMessage = 'HTTPS域名未配置，请在微信公众平台添加 miniapp.hh2mwa.top';
          } else if (err.errMsg.includes('request:fail ssl hand shake error')) {
            errorMessage = 'SSL证书错误，请检查HTTPS配置';
          } else if (err.errMsg.includes('request:fail timeout')) {
            errorMessage = '请求超时，请重试';
          } else if (err.errMsg.includes('request:fail')) {
            errorMessage = '网络连接失败，请检查网络或域名配置';
          }
        }

        wx.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        })
        reject(err)
      },
      complete: () => {
        wx.hideLoading()
      }
    })
  })
}

// 导出请求方法
export const get = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'GET',
    data,
    ...options
  })
}

export const post = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'POST',
    data,
    ...options
  })
}

export const put = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'PUT',
    data,
    ...options
  })
}

export const del = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'DELETE',
    data,
    ...options
  })
}

// 文件上传方法
export const uploadFile = (filePath, formData = {}) => {
  return new Promise((resolve, reject) => {
    // 获取存储的token
    const userSession = wx.getStorageSync('userSession')
    const token = userSession ? userSession.token : ''

    console.log('上传文件到:', `${BASE_URL}/app/common/fileUpload`)
    console.log('BASE_URL:', BASE_URL)

    wx.showLoading({ title: '上传中...', mask: true })

    wx.uploadFile({
      url: `${BASE_URL}/app/common/fileUpload`,
      filePath: filePath,
      name: 'file',
      formData: formData,
      header: {
        'Content-Type': 'multipart/form-data',
        'token': token,
      },
      success: (res) => {
        try {
          const data = JSON.parse(res.data)
          if (data.code === '200') {
            resolve(data)
          } else {
            wx.showToast({
              title: data.msg || '上传失败',
              icon: 'none'
            })
            reject(data)
          }
        } catch (error) {
          wx.showToast({
            title: '上传失败',
            icon: 'none'
          })
          reject(error)
        }
      },
      fail: (err) => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
        reject(err)
      },
      complete: () => {
        wx.hideLoading()
      }
    })
  })
}