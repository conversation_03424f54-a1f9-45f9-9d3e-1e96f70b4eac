import { post } from './utils/request'

App({
  onLaunch() {
    // 添加全局请求拦截器
    const checkToken = () => {
      const userSession = wx.getStorageSync('userSession')
      if (userSession && userSession.token) {
        // 调用校验token接口
        post('/app/auth/checkToken', {
          token: userSession.token
        }).then(res => {
          console.log("调用校验token接口: ", res)
          if (res.code === '200') {
            // token有效，更新用户信息并跳转到首页
            console.log("token验证成功，用户信息:", res.content)

            // 更新本地存储的用户信息
            const updatedUserSession = {
              ...userSession,
              userId: res.content.id,
              userName: res.content.userName,
              userType: res.content.type
            }
            wx.setStorageSync('userSession', updatedUserSession)

            // 跳转到首页
            wx.switchTab({
              url: '/pages/index/index'
            })
          } else {
            // token无效，清除登录信息并跳转到登录页
            wx.clearStorageSync()
            wx.redirectTo({
              url: '/pages/login/login'
            })
          }
        }).catch(() => {
          // 请求失败也清除登录信息
          wx.clearStorageSync()
          wx.redirectTo({
            url: '/pages/login/login'
          })
        })
      } else {
        // 没有token，跳转到登录页
        wx.redirectTo({
          url: '/pages/login/login'
        })
      }
    }
    
    // 如果是登录，则不检查token启动时检查一次
    const isLoggedIn = wx.getStorageSync('isLoggedIn') || false
    if (isLoggedIn) {
      checkToken()
    }
  }
})